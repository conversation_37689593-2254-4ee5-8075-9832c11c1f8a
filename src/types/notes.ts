/**
 * Note Templates and Task Notes Related Types
 *
 * This file contains all types related to the templated notes functionality,
 * including note templates, template fields, task notes, and related operations.
 */

import { z } from 'zod';

// Field Types
export type FieldType = 'text' | 'number' | 'date' | 'command';

export interface TemplateField {
  id: string;
  label: string;
  type: FieldType;
  required: boolean;
  placeholder?: string;
  order: number;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
  command?: string;
}

// Note Template
export interface NoteTemplate {
  id: string;
  name: string;
  description?: string;
  fields: TemplateField[];
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

// Task Note
export interface TaskNote {
  id: string;
  taskId: string;
  templateId: string;
  templateName: string; // Cached for display
  fieldValues: Record<string, any>; // Field ID -> Value mapping
  createdAt: string;
  updatedAt: string;
}

// Field Value Types
export interface FieldValue {
  fieldId: string;
  value: string | number | Date | null;
  type: FieldType;
}

// Zod Validation Schemas
export const TemplateFieldSchema = z.object({
  id: z.string().min(1),
  label: z.string().min(1).max(100),
  type: z.enum(['text', 'number', 'date', 'command']),
  required: z.boolean(),
  placeholder: z.string().optional(),
  order: z.number().min(0),
  validation: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional(),
  }).optional(),
  command: z.string().optional(),
});

export const NoteTemplateSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  fields: z.array(TemplateFieldSchema),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  isActive: z.boolean(),
});

export const TaskNoteSchema = z.object({
  id: z.string().min(1),
  taskId: z.string().min(1),
  templateId: z.string().min(1),
  templateName: z.string().min(1),
  fieldValues: z.record(z.any()),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Form Data Types
export interface TemplateFormData {
  name: string;
  description: string;
  fields: TemplateField[];
}

export interface FieldFormData {
  label: string;
  type: FieldType;
  required: boolean;
  placeholder: string;
  validation: {
    min: string;
    max: string;
    pattern: string;
  };
}

// Component Props Interfaces
export interface TemplateBuilderProps {
  templates: NoteTemplate[];
  onCreateTemplate: (template: Omit<NoteTemplate, 'id' | 'createdAt' | 'updatedAt'>) => Promise<NoteTemplate>;
  onUpdateTemplate: (templateId: string, updates: Partial<NoteTemplate>) => Promise<NoteTemplate>;
  onDeleteTemplate: (templateId: string) => Promise<void>;
  onSelectTemplate: (template: NoteTemplate | null) => void;
  selectedTemplate: NoteTemplate | null;
}

export interface NoteEditorProps {
  taskId: string;
  template: NoteTemplate | null;
  existingNote?: TaskNote | null;
  onSaveNote: (note: Omit<TaskNote, 'id' | 'createdAt' | 'updatedAt'>) => Promise<TaskNote>;
  onUpdateNote: (noteId: string, updates: Partial<TaskNote>) => Promise<TaskNote>;
  onDeleteNote: (noteId: string) => Promise<void>;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

export interface NotesListProps {
  taskId: string;
  notes: TaskNote[];
  onEditNote: (note: TaskNote) => void;
  onDeleteNote: (noteId: string) => void;
  onCreateNote: () => void;
}

export interface FieldEditorProps {
  field: TemplateField;
  onUpdateField: (field: TemplateField) => void;
  onDeleteField: (fieldId: string) => void;
  onMoveField: (fieldId: string, direction: 'up' | 'down') => void;
  isFirst: boolean;
  isLast: boolean;
}

// Hook Return Types
export interface UseNoteTemplatesReturn {
  templates: NoteTemplate[];
  createTemplate: (template: Omit<NoteTemplate, 'id' | 'createdAt' | 'updatedAt'>) => Promise<NoteTemplate>;
  updateTemplate: (templateId: string, updates: Partial<NoteTemplate>) => Promise<NoteTemplate>;
  deleteTemplate: (templateId: string) => Promise<void>;
  getTemplateById: (templateId: string) => NoteTemplate | undefined;
  getActiveTemplates: () => NoteTemplate[];
  isLoading: boolean;
  error: string | null;
}

export interface UseTaskNotesReturn {
  notes: TaskNote[];
  createNote: (note: Omit<TaskNote, 'id' | 'createdAt' | 'updatedAt'>) => Promise<TaskNote>;
  updateNote: (noteId: string, updates: Partial<TaskNote>) => Promise<TaskNote>;
  deleteNote: (noteId: string) => Promise<void>;
  getNotesByTaskId: (taskId: string) => TaskNote[];
  getNoteById: (noteId: string) => TaskNote | undefined;
  getNotesStats: () => {
    totalNotes: number;
    templatesUsed: string[];
    lastNoteDate?: string;
    notesByTemplate: Record<string, number>;
  };
  isLoading: boolean;
  error: string | null;
}

// Utility Types
export interface TemplateStats {
  totalTemplates: number;
  activeTemplates: number;
  totalNotes: number;
  templatesWithNotes: number;
}

export interface NoteValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: Record<string, string>;
}
